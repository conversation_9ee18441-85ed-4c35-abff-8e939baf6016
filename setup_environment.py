# ==================== SlotGAT 环境配置脚本 (最终修复版) ====================
print("🚀 开始配置 SlotGAT 环境...")

import os
import sys
import subprocess

def run_command(cmd):
    """运行命令并显示输出"""
    print(f"执行: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"❌ 错误: {result.stderr}")
        return False
    else:
        print("✅ 成功")
        return True

print(f"当前Python版本: {sys.version}")

# 1. 安装兼容版本
print("\n📦 安装兼容版本...")

# 卸载冲突包
run_command("pip uninstall -y torch torchvision torchaudio dgl torch-geometric torch-sparse torch-scatter torch-cluster")

# 安装PyTorch 1.13.1 (不指定torchaudio的cu117版本)
print("安装PyTorch 1.13.1...")
run_command("pip install torch==1.13.1+cu117 torchvision==0.15.0+cu117 --extra-index-url https://download.pytorch.org/whl/cu117")
run_command("pip install torchaudio==0.13.1")  # 单独安装CPU版本

# 安装NumPy
print("安装NumPy...")
run_command("pip install numpy==1.24.3")

# 安装DGL
print("安装DGL...")
run_command("pip install dgl==1.0.1+cu117 -f https://data.dgl.ai/wheels/cu117/repo.html")

# 安装PyTorch Geometric
print("安装PyTorch Geometric...")
run_command("pip install torch-geometric==2.2.0")

# 安装PyG依赖 (使用预编译wheel)
print("安装PyG依赖...")
run_command("pip install torch-sparse==0.6.16 torch-scatter==2.1.0 torch-cluster==1.6.0 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html")

# 安装其他依赖
print("安装其他依赖...")
run_command("pip install networkx==2.8.4 scikit-learn==1.2.1 scipy==1.10.0")

# 验证安装
print("\n🔍 验证安装...")
try:
    import torch
    import dgl
    import torch_geometric
    import torch_sparse
    import torch_scatter
    import torch_cluster
    import networkx as nx
    import scipy
    import sklearn
    import numpy as np
    
    print("✅ 所有依赖安装成功!")
    print(f"  PyTorch: {torch.__version__}")
    print(f"  DGL: {dgl.__version__}")
    print(f"  PyG: {torch_geometric.__version__}")
    print(f"  torch_sparse: {torch_sparse.__version__}")
    print(f"  torch_scatter: {torch_scatter.__version__}")
    print(f"  torch_cluster: {torch_cluster.__version__}")
    print(f"  NetworkX: {nx.__version__}")
    print(f"  SciPy: {scipy.__version__}")
    print(f"  Scikit-learn: {sklearn.__version__}")
    print(f"  NumPy: {np.__version__}")
    
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("🔄 请重启运行时后重新运行此脚本")

# 4. 挂载Google Drive
print("\n💾 挂载 Google Drive...")
try:
    from google.colab import drive
    drive.mount('/content/drive')
    print("✅ Google Drive 挂载成功")
except:
    print("⚠️ 不在Colab环境或Drive已挂载")

# 5. 切换到项目目录
print("\n📁 切换到项目目录...")
project_path = '/content/drive/MyDrive/SlotGAT_Project/SlotGAT_ICML23-new(1)（学长重置版）/SlotGAT_ICML23-new（1）/NC/methods/SlotGAT'

if os.path.exists(project_path):
    os.chdir(project_path)
    print(f"✅ 当前目录: {os.getcwd()}")
    
    # 检查关键文件
    key_files = ['GNN_bak.py', 'run_analysis.py', 'run_train_slotGAT_on_all_dataset.py']
    for file in key_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} 不存在")
else:
    print(f"❌ 项目目录不存在: {project_path}")
    print("请检查路径是否正确")

# 6. 创建必要目录
print("\n📂 创建必要目录...")
dirs = ['outputs', 'log', 'checkpoint', 'analysis']
for dir_name in dirs:
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
        print(f"  ✅ 创建 {dir_name}/")
    else:
        print(f"  📁 {dir_name}/ 已存在")

print("\n🎉 环境配置完成!")
print("🚀 现在可以运行:")
print("  训练: !python run_train_slotGAT_on_all_dataset.py")
print("  评估: !python run_use_slotGAT_on_all_dataset.py")

if __name__ == "__main__":
    pass





